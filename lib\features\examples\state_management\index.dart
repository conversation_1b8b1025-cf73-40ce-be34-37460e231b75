import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';

import 'controller.dart';

class ObsExamplePage extends StatelessWidget {
  const ObsExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    ObsExamplePageController controller = Get.put(ObsExamplePageController());
    return Scaffold(
      appBar: AppBar(title: const Text("Obs Example")),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: controller.regetLocation,
        label: const Text("get location"),
      ),
      body: Center(
        child: ObsVariableBuilder(
          obs: controller.location,
          builder: (context, status, error, location) {
            if (status == VariableStatus.Loading) {
              return const CircularProgressIndicator();
            } else if (status == VariableStatus.HasData) {
              return Text(location!.toString());
            } else {
              return Text(
                error!,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              );
            }
          },
        ),
      ),
    );
  }
}
