import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/utils/validator.dart';

import '../svg_icon.dart';
import 'prefix.dart';

class PasswordField extends StatefulWidget {
  final String? hintText;
  final TextEditingController controller;
  final String? Function(String? value)? validator;

  const PasswordField({
    super.key,
    this.hintText,
    required this.controller,
    this.validator,
  });

  @override
  State<PasswordField> createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordField> {
  bool isPasswordHidden = true;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      obscureText: isPasswordHidden,
      textInputAction: TextInputAction.next,
      validator: widget.validator ?? Validator.validatePass,
      decoration: InputDecoration(
        hintText: widget.hintText ?? tr(LocaleKeys.write_password),
        errorMaxLines: 3,
        prefixIcon: FieldPrefix(child: SvgIcon(Assets.icons.lock)),
        suffixIcon: IconButton(
          onPressed:
              () => setState(() {
                isPasswordHidden = !isPasswordHidden;
              }),
          icon:
              isPasswordHidden
                  ? SvgIcon(Assets.icons.eyeClosed, color: StyleRepo.grey)
                  : SvgIcon(
                    Assets.icons.eyeOpened,
                    color: context.appColorScheme.secondaryColor,
                  ),
        ),
      ),
    );
  }
}
