import 'package:get/get.dart';
import 'package:tredo/core/services/state_management/obs.dart';

import '../../../../core/services/rest_api/rest_api.dart';
import 'models/post.dart';

class RequestAPIObjectExamplePageController extends GetxController {
  Request<PostModel> postRequest = Request<PostModel>(
    endPoint: "https://jsonplaceholder.typicode.com/posts/1",
    isFullURL: true,
    fromJson: PostModel.fromJson,
  );

  ObsVar<PostModel> post = ObsVar(null);
  fetchData() async {
    ResponseModel response = await APIService.instance.requestAPI(postRequest);
    if (response.success) {
      post.value = response.data;
    } else {
      post.error = response.message;
    }
  }

  @override
  void onInit() {
    //NOTE - you don't need this
    // it will be injected by default at the start of app
    Get.put(
      APIService(headers: {"Accept": "application/json"}, withLog: false),
    );

    fetchData();
    super.onInit();
  }

  @override
  void onClose() {
    postRequest.stop();
    super.onClose();
  }
}
