import 'package:flutter/services.dart';

import '../localization/localization.dart';
import '../style/themes.dart';
import 'role.dart';

/// Here you can define global const data
abstract class Default {
  static const AppLocalization defaultLocale = AppLocalization.en;

  static const Role defaultRole = NewUser();

  static const String appTitle = 'Tredo';

  static const countryCode = '+963';

  static const AppTheme defaultTheme = AppTheme.light;

  static preferredOrientation() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }
}
