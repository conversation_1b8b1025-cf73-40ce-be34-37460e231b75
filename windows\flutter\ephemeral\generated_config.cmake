# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\src\\versions\\3.29.3" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\work\\ixCoders\\current\\tredo\\tredo" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\src\\versions\\3.29.3"
  "PROJECT_DIR=D:\\work\\ixCoders\\current\\tredo\\tredo"
  "FLUTTER_ROOT=C:\\src\\versions\\3.29.3"
  "FLUTTER_EPHEMERAL_DIR=D:\\work\\ixCoders\\current\\tredo\\tredo\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\work\\ixCoders\\current\\tredo\\tredo"
  "FLUTTER_TARGET=D:\\work\\ixCoders\\current\\tredo\\tredo\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\work\\ixCoders\\current\\tredo\\tredo\\.dart_tool\\package_config.json"
)
