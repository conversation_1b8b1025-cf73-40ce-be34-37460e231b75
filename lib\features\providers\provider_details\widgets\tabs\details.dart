import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/demo/lorem.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/widgets/general/data_sections/contact_details.dart';
import 'package:tredo/core/widgets/general/data_sections/description.dart';

class DetailsTab extends StatelessWidget {
  const DetailsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverList(
        delegate: SliverChildListDelegate([
          const DescriptionSection(description: Lorem.parag),
          const Gap(16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                [
                  "#food",
                  "#yummy",
                  "#restaurant",
                  "#delicious",
                  "#cuisine",
                  "#food",
                  "#yummy",
                  "#restaurant",
                  "#delicious",
                  "#cuisine",
                ].map((e) => Chip(label: Text(e))).toList(),
          ),
          const Gap(16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                tr(LocaleKeys.contact_details),
                style: context.textTheme.titleLarge,
              ),
              const Gap(12),
              const ContactDetails(
                contacts: [
                  '+963 999 999 999',
                  '+963 999 999 999',
                  '+963 999 999 999',
                  '+963 999 999 999',
                  '+963 999 999 999',
                  '+963 999 999 999',
                ],
              ),
            ],
          ),
          const Gap(16),
        ]),
      ),
    );
  }
}
