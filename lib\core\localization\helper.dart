import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'localization.dart';

extension ContentTr on Map<String, dynamic> {
  String tr(BuildContext context) {
    String languageCode =
        EasyLocalization.of(context)!.currentLocale!.languageCode;
    return this[languageCode] ??
        this[AppLocalization.en.value] ??
        "Not existed";
  }
}
