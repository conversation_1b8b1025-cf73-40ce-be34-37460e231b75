import 'package:get/get.dart';
import 'package:tredo/core/utils/timer.dart';

import 'models/nav.dart';

const int _kResendTime = 30;

class VerificationPageController extends GetxController with ResendCodeTimer {
  final VerificationPageNav nav;
  VerificationPageController(this.nav);

  @override
  void onInit() {
    startTimer(_kResendTime);
    super.onInit();
  }

  final Rx<String> _code = "".obs;
  String get code => _code.value;
  set code(String value) => _code.value = value;

  final Rx<String> _error = "".obs;
  String get error => _error.value;
  set error(String value) => _error.value = value;

  void verify() async {
    error = "";

    await 2.seconds.delay();
    if (code != "1111") {
      error = "Invalid code";
    } else {
      Get.back(result: true);
    }
  }

  void resend() async {
    startTimer(_kResendTime);
  }
}
