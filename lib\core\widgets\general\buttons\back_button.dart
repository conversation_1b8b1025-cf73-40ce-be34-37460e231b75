import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../svg_icon.dart';

class AppBackButton extends StatelessWidget {
  final bool? Function()? onBack;
  final bool handleDeepLink;
  final bool withBorder;

  const AppBackButton({
    super.key,
    this.onBack,
    this.handleDeepLink = false,
    this.withBorder = false,
  });
  onPop() {
    if (onBack != null) {
      if (!(onBack!.call() ?? true)) {
        return;
      }
    }
    if (handleDeepLink) {
      //TODO - deep links

      // DeepLinks.pageBack(context);
    } else {
      Get.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget iconWidget = SvgIcon(Assets.icons.arrowBack, size: 24);

    if (Directionality.of(context) == TextDirection.rtl) {
      iconWidget = Transform(
        transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
        alignment: Alignment.center,
        transformHitTests: false,
        child: iconWidget,
      );
    }

    if (withBorder) {
      iconWidget = Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPop,
          borderRadius: BorderRadius.circular(4),
          child: Container(
            constraints: const BoxConstraints(maxHeight: 50, maxWidth: 50),
            decoration: BoxDecoration(
              border: Border.all(color: IconTheme.of(context).color!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: iconWidget,
          ),
        ),
      );
    } else {
      iconWidget = IconButton(onPressed: onPop, icon: iconWidget);
    }

    return iconWidget;
  }
}
