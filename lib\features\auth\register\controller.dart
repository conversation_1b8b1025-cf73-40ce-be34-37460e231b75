import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:tredo/core/config/app_builder.dart';
import 'package:tredo/core/config/role.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/features/auth/verification/models/nav.dart';

class RegisterPageController extends GetxController {
  AppBuilder appBuilder = Get.find();

  final formKey = GlobalKey<FormState>();

  late TextEditingController firstName,
      lastName,
      phone,
      password,
      confirmPassword;

  @override
  void onInit() {
    firstName = TextEditingController();
    lastName = TextEditingController();
    phone = TextEditingController();
    password = TextEditingController();
    confirmPassword = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    firstName.dispose();
    lastName.dispose();
    phone.dispose();
    password.dispose();
    confirmPassword.dispose();
    super.onClose();
  }

  //SECTION - Terms and condition
  final Rx<bool> _isTermsAccepted = false.obs;
  bool get isTermsAccepted => _isTermsAccepted.value;
  set isTermsAccepted(bool value) => _isTermsAccepted.value = value;

  openTerms() {
    Nav.to(Pages.terms);
  }
  //!SECTION

  Future<void> register() async {
    if (!formKey.currentState!.validate()) return;

    bool? result = await Nav.to(
      Pages.verification,
      arguments: VerificationPageNav(phone: phone.text),
    );
    if (result ?? false) {
      appBuilder.setRole(const User());
      await appBuilder.role.initialize();

      Nav.offUntil(appBuilder.role.landing, (_) => false);
    }
  }

  void login() {
    if (NavigationHistoryObserver().history
        .asList()
        .map((route) => route.settings.name)
        .contains(Pages.login.value)) {
      Get.until((route) => route.settings.name == Pages.login.value);
    } else {
      Nav.offAll(Pages.login);
    }
  }
}
