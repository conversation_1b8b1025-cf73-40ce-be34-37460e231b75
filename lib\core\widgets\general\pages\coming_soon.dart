import 'package:flutter/material.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/features/auth/widgets/appbar.dart';

class ComingSoonPage extends StatelessWidget {
  const ComingSoonPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const AppScaffold(
      appBar: AuthAppBar(title: Text("Coming Soon")),
      body: Center(child: Text("Coming Soon")),
    );
  }
}
