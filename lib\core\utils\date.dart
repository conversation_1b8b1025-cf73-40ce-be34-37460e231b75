import 'package:flutter/material.dart';

extension DateUtils on DateTime {
  String getdayName(int index) {
    if (index == 1) {
      return 'Monday';
    } else if (index == 2) {
      return 'Tuesday';
    } else if (index == 3) {
      return 'Wednesday';
    } else if (index == 4) {
      return 'Thursday';
    } else if (index == 5) {
      return 'Friday';
    } else if (index == 6) {
      return 'Saturday';
    } else {
      return 'Sunday';
    }
  }

  String getMonthName(int index) {
    switch (index) {
      case 1:
        return 'January';
      case 2:
        return 'February';
      case 3:
        return 'March';
      case 4:
        return 'April';
      case 5:
        return 'May';
      case 6:
        return 'June';
      case 7:
        return 'July';
      case 8:
        return 'August';
      case 9:
        return 'September';
      case 10:
        return 'October';
      case 11:
        return 'November';
      case 12:
        return 'December';
      default:
        return '';
    }
  }

  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateToCheck = DateTime(year, month, day);
    if (today == dateToCheck) return true;
    return false;
  }

  static TimeOfDay createTimeOfDayFromMinutes(int minutes) {
    int hours = minutes ~/ 60;
    int mins = minutes % 60;

    final timeOfDay = TimeOfDay(hour: hours, minute: mins);
    return timeOfDay;
  }

  static int fromTimeStringToInt(String s) {
    final splitted = s.split(":");
    return int.parse(splitted[0]) * 60 + int.parse(splitted[1]);
  }

  static String fromIntToTimeString(int time) {
    return "${time ~/ 60}:${time % 60}";
  }
}
