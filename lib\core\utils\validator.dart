import 'package:easy_localization/easy_localization.dart';

import '../localization/strings.dart';

abstract class Validator {
  // static String? requiredEmail(String? email) {
  //   email = email?.trim();
  //   bool valid = RegExp(
  //           r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
  //       .hasMatch(email!);
  //   return !valid ? LocaleKeys.email_validation_message.tr() : null;
  // }

  // static String? optionalEmail(String? email) {
  //   if (email == null || email.trim().isEmpty) return null;

  //   return requiredEmail(email);
  // }

  static String? notNull(dynamic value) =>
      (value == null ||
              (value is String && value.trim().isEmpty) ||
              (value is List && value.isEmpty))
          ? LocaleKeys.this_field_is_required.tr()
          : null;

  static String? phone(String? value) {
    String? error = notNull(value);
    if (error != null) {
      return error;
    }
    if (value!.length != 10) {
      return LocaleKeys.phone_number_must_be_at_least_10_digits.tr();
    }

    return null;
  }

  // static String? name(String? value) {
  //   String? error = notNull(value);
  //   if (error != null) return error;

  //   if (!RegExp(r"^[^0-9]{3,50}$").hasMatch(value!)) {
  //     return LocaleKeys.name_validation_message.tr();
  //   }
  //   return null;
  // }

  // static String? countValidation(String? value, int minCount) {
  //   String? error = notNull(value);
  //   if (error != null) return error;

  //   if (value!.length < minCount) {
  //     return LocaleKeys.min_count_validation_message.tr(args: ["$minCount"]);
  //   }
  //   return null;
  // }

  static String? validatePass(String? value) {
    String? error = notNull(value);
    if (error != null) return error;

    final RegExp strongPasswordRegex = RegExp(
      r'^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*]).{8,}$',
    );
    if (!strongPasswordRegex.hasMatch(value!)) {
      return LocaleKeys.password_validation_message.tr();
    } else {
      return null;
    }
  }

  static String? confirmPassword(String pass, String confirm) {
    if (pass != confirm) {
      return LocaleKeys.passwords_do_not_match.tr();
    }
    return null;
  }
}
