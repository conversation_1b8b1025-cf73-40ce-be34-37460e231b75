import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/features/auth/reset_password/models/nav.dart';
import 'package:tredo/features/auth/verification/models/nav.dart';

class ForgetPasswordPageController extends GetxController {
  late TextEditingController phone;

  @override
  void onInit() {
    phone = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    phone.dispose();
    super.onClose();
  }

  final Rx<bool> _isPhoneValid = false.obs;
  bool get isPhoneValid => _isPhoneValid.value;
  set isPhoneValid(bool value) => _isPhoneValid.value = value;

  sendCode() async {
    bool? result = await Nav.to(
      Pages.verification,
      arguments: VerificationPageNav(phone: phone.text),
    );

    if (result ?? false) {
      bool? result = await Nav.to(
        Pages.reset_password,
        arguments: ResetPasswordPageNav(mode: ResetPasswordMode.reset),
      );

      if (result ?? false) {
        Nav.offUntil(Pages.login, (_) => false);
      }
    }
  }
}
