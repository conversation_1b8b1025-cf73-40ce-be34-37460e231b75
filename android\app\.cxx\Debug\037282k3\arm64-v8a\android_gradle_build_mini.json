{"buildFiles": ["C:\\src\\versions\\3.29.3\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\work\\ixCoders\\current\\tredo\\tredo\\android\\app\\.cxx\\Debug\\037282k3\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\work\\ixCoders\\current\\tredo\\tredo\\android\\app\\.cxx\\Debug\\037282k3\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}