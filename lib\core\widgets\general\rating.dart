// import 'package:flutter/material.dart';
// import 'package:flutter_rating_bar/flutter_rating_bar.dart';
// import 'package:renva/core/style/style.dart';

// import 'svg_icon.dart';

// class AppRatingWidget extends StatelessWidget {
//   final double itemSize;
//   final double rating;
//   const AppRatingWidget({super.key, this.itemSize = 20, required this.rating});

//   @override
//   Widget build(BuildContext context) {
//     return RatingBar.builder(
//       itemBuilder: (_, __) => SvgIcon(
//         Assets.icons.star,
//         color: context.appColorScheme.ratingColor,
//       ),
//       itemCount: rating.round(),
//       initialRating: rating,
//       itemSize: itemSize,
//       allowHalfRating: true,
//       unratedColor: Colors.transparent,
//       ignoreGestures: true,
//       onRatingUpdate: (_) {},
//     );
//   }
// }
