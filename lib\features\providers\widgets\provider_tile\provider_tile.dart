import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/demo/media.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/core/widgets/general/tags_row.dart';
import 'package:tredo/core/widgets/image.dart';
import 'package:tredo/features/providers/provider_details/models/nav.dart';
import 'style/style.dart';

class ProviderTile extends StatelessWidget {
  const ProviderTile({super.key});

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.providerTileTheme;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap:
            () => Nav.to(Pages.provider, arguments: ProviderDetailsPageNav()),
        borderRadius: BorderRadius.circular(13),
        child: Container(
          height: 110,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.fromBorderSide(cardTheme.side),
            borderRadius: BorderRadius.circular(13),
          ),
          child: Row(
            children: [
              AspectRatio(
                aspectRatio: 1,
                child: AppImage(
                  path: DemoMedia.getAppRandomImage,
                  decoration: BoxDecoration(
                    boxShadow: cardTheme.imageShadow,
                    borderRadius: BorderRadius.circular(9),
                  ),
                ),
              ),
              const Gap(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      "Mohammad Enawe",
                      style: cardTheme.nameStyle,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        SvgIcon(
                          Assets.icons.eyeOutlined,
                          size: 18,
                          color: cardTheme.viewsStyle.color,
                        ),
                        const Gap(4),
                        Text(
                          LocaleKeys.n_views.plural(3),
                          style: cardTheme.viewsStyle,
                        ),
                      ],
                    ),
                    const TagsRow(
                      tags: [
                        "#food",
                        "#restaurant",
                        "#delicious",
                        "#cuisine",
                        "#yummy",
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                height: 35,
                width: 35,
                decoration: BoxDecoration(
                  color: context.appColorScheme.secondaryColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: context.appColorScheme.onPrimaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
