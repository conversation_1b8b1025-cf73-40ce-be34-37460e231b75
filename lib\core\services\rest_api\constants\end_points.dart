// ignore_for_file: constant_identifier_names, non_constant_identifier_names

abstract class EndPoints {
  //##########  Base Url  ##########
  // static const String baseUrl = 'https://study.allinone.college/public/api/';
  static const String baseUrl = 'https://renva.nu/api/';

  //SECTION - Auth
  static const login = "v1/login";
  static const signup = "v1/register";
  static const verify_code = "v1/verify-otp";
  static const resend_code = "v1/resend-otp";
  static const complete_user_info = "v1/update";
  static const forget_password = "v1/forget_password";
  static const reset_password = "v1/reset_password";
  static const logout = "v1/logout";
  //!SECTION

  //SECTION - Profile
  static const my_profile = "v1/profile";
  static const join_as_provider = "v1/join_as_provider";
  static provider_profile(int id) => "v1/provider/$id";
  static const update_provider = "v1/provider/update";
  //!SECTION

  //SECTION - Categories
  static const categories = "v1/provider_categories";
  static subcategories(int id) => "v1/sub_categories/$id";

  //!SECTION

  //SECTION - Order
  static const create_order = "v1/orders";
  static const orders_by_status = "v1/orders_by_status";
  static const customer_orders = "v1/customer/orders";
  static const provider_orders = "v1/provider_orders";
  static const end_order = "v1/orders/provider/end";
  static const cancellation_reasons = "v1/order_cancel_reasons";
  static const cancel_order = "v1/orders/cancel";
  static const provider_cancel_order = "v1/orders/provider/cancel";
  static delete_order(int id) => "v1/orders/$id";
  static const customer_order_review = "v1/orders/customer/review";
  static const provider_order_review = "v1/orders/provider/review";
  //!SECTION

  //SECTION - Offer
  static const create_offer = "v1/offers";
  static order_offers(int id) => "v1/order/offers/$id";
  static const accept_offer = "v1/offers/accept";
  static const decline_offer = "v1/offers/decline";
  //!SECTION

  //SECTION - Chat
  static const rooms = "v1/chats";
  static messages(int id) => "v1/messages/$id";
  static const message = "v1/message";
  //!SECTION

  //SECTION - Reels
  static const create_reel = "v1/stories";
  static const delete_reel = "v1/stories";
  static const my_reels = "v1/my_stories";
  static const reels_groups = "v1/provider_stories";
  static const reels = "v1/stories";
  //!SECTION
}
