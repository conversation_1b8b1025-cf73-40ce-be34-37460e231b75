import 'package:flutter/material.dart';

abstract class StyleRepo {
  static const MaterialColor violate = MaterialColor(0xFFCE74EA, {
    50: Color(0xFFFAF1FD),
    100: Color(0xFFF0D4F8),
    200: Color(0xFFE8BFF5),
    300: Color(0xFFDEA2F1),
    400: Color(0xFFD890EE),
    500: Color(0xFFCE74EA),
    600: Color(0xFFBB6AD5),
    700: Color(0xFF9252A6),
    800: Color(0xFF714081),
    900: Color(0xFF573162),
  });

  static const MaterialColor blue = MaterialColor(0xFF284070, {
    50: Color(0xFFEAECF1),
    100: Color(0xFFBCC4D3),
    200: Color(0xFF9CA7BD),
    300: Color(0xFF6F7F9F),
    400: Color(0xFF53668D),
    500: Color(0xFF284070),
    600: Color(0xFF243A66),
    700: Color(0xFF1C2D50),
    800: Color(0xFF16233E),
    900: Color(0xFF111B2F),
  });

  static const MaterialColor purple = MaterialColor(0xFFD2DFF9, {
    50: Color(0xFFFBFCFE),
    100: Color(0xFFF1F5FD),
    200: Color(0xFFEAF0FC),
    300: Color(0xFFE1EAFB),
    400: Color(0xFFDBE5FA),
    500: Color(0xFFD2DFF9),
    600: Color(0xFFBFCBE3),
    700: Color(0xFF959EB1),
    800: Color(0xFF747B89),
    900: Color(0xFF585E96),
  });

  static const black = Colors.black;

  static const lightGrey = Color(0xFFEAEAEA);
  static const grey = Color(0xFFB0B0B0);
  static const darkGrey = Color(0xFF919191);

  static const red = Color(0xFFD85A5A);
  static const lightRed = Color(0xFFFCE1E2);

  static const green = Color(0xFF44C838);
}
