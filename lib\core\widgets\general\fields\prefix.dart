import 'package:flutter/material.dart';
import 'package:tredo/core/style/style.dart';

class FieldPrefix extends StatelessWidget {
  final Widget child;
  const FieldPrefix({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return IconTheme(
      data: IconTheme.of(
        context,
      ).copyWith(color: context.appColorScheme.secondaryColor),
      child: DefaultTextStyle(
        style: TextStyle(color: context.appColorScheme.secondaryColor),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: child,
        ),
      ),
    );
  }
}
