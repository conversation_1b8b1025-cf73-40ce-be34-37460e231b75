import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/demo/media.dart';
import 'package:tredo/core/widgets/image.dart';

import '../controller.dart';

class HomeAppBar extends GetView<HomePageController> {
  const HomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Row(
        children: [
          AppImage(
            path: DemoMedia.getAppRandomImage,
            type: ImageType.CachedNetwork,
            height: 50,
            width: 50,
            decoration: const BoxDecoration(shape: BoxShape.circle),
          ),
          const Gap(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Mohammad Enawe", style: context.textTheme.titleMedium),
                Text(
                  "<PERSON><PERSON>-<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>",
                  style: context.textTheme.labelMedium,
                ),
              ],
            ),
          ),
          const Gap(12),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.notifications_outlined),
          ),
        ],
      ),
    );
  }
}
