extension NumExtension on num {
  String get approximate {
    if (this < 1000) {
      return '$this';
    } else if (this < 1000000) {
      return '${this ~/ 1000} K';
    } else {
      return '${this ~/ 1000000} M';
    }
  }
}

extension IntExtension on int {
  String get formatSeconds {
    int minutes = (this / 60).floor();
    int remainingSeconds = this % 60;

    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = remainingSeconds.toString().padLeft(2, '0');

    return '$formattedMinutes:$formattedSeconds';
  }
}
