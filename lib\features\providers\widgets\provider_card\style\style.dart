// ignore_for_file: annotate_overrides

import 'package:flutter/material.dart';
import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';
import 'package:tredo/core/style/repo.dart';

part 'style.tailor.dart';

@TailorMixin()
class ProviderCardTheme extends ThemeExtension<ProviderCardTheme>
    with _$ProviderCardThemeTailorMixin {
  ProviderCardTheme({
    required this.backgroundColor,
    required this.side,
    required this.nameStyle,
    required this.viewsStyle,
  });

  final Color backgroundColor;
  final BorderSide side;
  final TextStyle nameStyle;
  final TextStyle viewsStyle;

  factory ProviderCardTheme.light(TextTheme textTheme) => ProviderCardTheme(
    backgroundColor: StyleRepo.purple.shade50,
    side: BorderSide(color: StyleRepo.purple.shade400),
    nameStyle: textTheme.titleMedium!,
    viewsStyle: textTheme.labelMedium!.copyWith(color: StyleRepo.grey),
  );
}
