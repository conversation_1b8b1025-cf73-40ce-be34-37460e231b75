import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/demo/media.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/image.dart';

import '../controller.dart';

class CarouselAds extends GetView<HomePageController> {
  final double aspectRatio;

  CarouselAds({super.key, this.aspectRatio = 16 / 9});

  final Rx<int> _currentAd = 0.obs;
  int get currentAd => _currentAd.value;
  set currentAd(int value) => _currentAd.value = value;

  @override
  Widget build(BuildContext context) {
    List<String> images = List.generate(4, (index) => DemoMedia.getRandomImage);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CarouselSlider(
          options: CarouselOptions(
            onPageChanged: (index, _) => currentAd = index,
            autoPlay: true,
            enlargeCenterPage: true,
            aspectRatio: aspectRatio,
          ),
          items: List.generate(
            images.length,
            (index) => InkWell(
              // onTap: () => controller.adAction(ads[index]),
              child: AspectRatio(
                aspectRatio: aspectRatio,
                child: AppImage(
                  width: double.infinity,
                  path: images[index],
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
              ),
            ),
          ),
        ),
        const Gap(12),
        SizedBox(
          height: 12,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              images.length,
              (index) => Obx(
                () => AnimatedContainer(
                  duration: 300.milliseconds,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  height: 10,
                  width: currentAd == index ? 30 : 10,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color:
                        currentAd == index
                            ? context.appColorScheme.secondaryColor
                            : StyleRepo.purple,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
