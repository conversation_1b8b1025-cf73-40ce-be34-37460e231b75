import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/utils.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

class SettingsTile extends StatelessWidget {
  final SvgGenImage icon;
  final String text;
  final Function()? onTap;

  const SettingsTile({
    super.key,
    required this.icon,
    required this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            SvgIcon(icon, color: context.appColorScheme.secondaryColor),
            const Gap(8),
            Expanded(child: Text(text, style: context.textTheme.titleMedium)),
            const Gap(8),
            const Icon(Icons.arrow_forward_ios_rounded),
          ],
        ),
      ),
    );
  }
}
