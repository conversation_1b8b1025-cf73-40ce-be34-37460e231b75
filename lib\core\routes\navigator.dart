import 'package:flutter/material.dart';

import 'routes.dart';
import 'package:get/get.dart';

abstract class Nav {
  static Future? to(Pages page, {dynamic arguments}) =>
      Get.toNamed(page.value, arguments: arguments);

  static Future? replacement(Pages page, {dynamic arguments}) =>
      Get.offNamed(page.value, arguments: arguments);

  static Future? offAll(Pages page, {dynamic arguments}) =>
      Get.offNamed(page.value, arguments: arguments);

  static Future? offUntil(Pages page, bool Function(Route<dynamic>) predicate,
          {dynamic arguments}) =>
      Get.offNamedUntil(page.value, predicate, arguments: arguments);
}
