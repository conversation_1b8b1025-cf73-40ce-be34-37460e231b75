import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/widgets/error_widget.dart';

import 'controller.dart';
import 'widgets/post_card.dart';

class RequestAPIObjectExamplePage extends StatelessWidget {
  const RequestAPIObjectExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    RequestAPIObjectExamplePageController controller = Get.put(
      RequestAPIObjectExamplePageController(),
    );
    return Scaffold(
      appBar: AppBar(title: const Text("Request Object Example")),
      body: Obx(() {
        if (controller.post.loading) {
          return const Center(child: CircularProgressIndicator());
        } else if (controller.post.hasError) {
          return AppErrorWidget(error: controller.post.error!);
        } else {
          return Center(child: PostCard(post: controller.post.value!));
        }
      }),
    );
  }
}
