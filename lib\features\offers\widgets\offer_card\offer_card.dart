import 'package:flutter/widgets.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/demo/media.dart';
import 'package:tredo/core/widgets/image.dart';
import 'style/style.dart';

class OfferCard extends StatelessWidget {
  final bool withProviderData;
  const OfferCard({super.key, this.withProviderData = true});

  static const double aspectRatio = .8;

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.offerCardTheme;
    return AspectRatio(
      aspectRatio: aspectRatio,
      child: Stack(
        children: [
          SizedBox.expand(
            child: AppImage(
              path: DemoMedia.getAppRandomImage,
              decoration: BoxDecoration(
                boxShadow: cardTheme.shadow,
                borderRadius: BorderRadius.vertical(
                  top: const Radius.circular(4),
                  bottom:
                      withProviderData ? Radius.zero : const Radius.circular(4),
                ),
              ),
            ),
          ),
          if (!withProviderData)
            const PositionedDirectional(
              bottom: 12,
              start: 12,
              child: _ProviderAvatar(),
            ),
          if (withProviderData)
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: cardTheme.providerDataBackgroundColor,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    const _ProviderAvatar(),
                    const Gap(8),
                    Expanded(
                      child: Text(
                        "Mohammad Enawe",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: cardTheme.providerNameStyle,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _ProviderAvatar extends StatelessWidget {
  const _ProviderAvatar();

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.offerCardTheme;
    return AppImage(
      path: DemoMedia.getAppRandomImage,
      height: 45,
      width: 45,
      decoration: BoxDecoration(
        color: cardTheme.providerAvatarBorderColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: cardTheme.providerAvatarBorderColor),
      ),
    );
  }
}
