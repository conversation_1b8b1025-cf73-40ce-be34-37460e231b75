import 'package:flutter/material.dart';
import 'package:get/utils.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

class ContactDetails extends StatelessWidget {
  final List contacts;
  const ContactDetails({super.key, required this.contacts});

  @override
  Widget build(BuildContext context) {
    return ChipTheme(
      data: ChipThemeData(
        backgroundColor: Colors.transparent,
        side: BorderSide(color: context.appColorScheme.secondaryColor),
        iconTheme: IconThemeData(color: context.appColorScheme.secondaryColor),
        labelStyle: context.textTheme.labelMedium!.copyWith(
          color: context.appColorScheme.secondaryColor,
        ),
      ),
      child: Wrap(
        spacing: 10,
        runSpacing: 16,
        children:
            contacts
                .map(
                  (e) => ActionChip(
                    onPressed: () {},
                    avatar: SvgIcon(Assets.icons.earth),
                    label: Text(e),
                  ),
                )
                .toList(),
      ),
    );
  }
}
