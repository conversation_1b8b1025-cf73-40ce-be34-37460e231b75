import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/features/providers/widgets/provider_tile/provider_tile.dart';

import 'controller.dart';
import 'widgets/app_bar.dart';

class ProvidersPage extends GetView<ProvidersPageController> {
  const ProvidersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const ProvidersAppBar(),
      body: ListView.separated(
        padding: const EdgeInsets.only(
          top: 24,
          bottom: 70,
          left: 16,
          right: 16,
        ),
        itemCount: 12,
        separatorBuilder: (_, __) => const Gap(12),
        itemBuilder: (context, index) => const ProviderTile(),
      ),
    );
  }
}
