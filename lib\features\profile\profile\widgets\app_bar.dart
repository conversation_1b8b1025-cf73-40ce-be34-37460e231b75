import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:tredo/core/localization/strings.dart';

class ProfileAppBar extends StatelessWidget {
  final double bottomPadding;
  const ProfileAppBar({super.key, required this.bottomPadding});

  static const height = 100.0;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.paddingOf(context).top + height + bottomPadding,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Center(
            child: Padding(
              padding: EdgeInsets.only(bottom: bottomPadding),
              child: Text(
                tr(LocaleKeys.settings),
                style: context.textTheme.headlineSmall,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
