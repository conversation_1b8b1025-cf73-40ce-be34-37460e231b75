import 'package:flutter/material.dart';

class AppElevatedButton extends StatefulWidget {
  final Function()? onPressed;
  final Widget child;
  final Color? backgroundColor;
  final Color? foregroundColor;
  const AppElevatedButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  State<AppElevatedButton> createState() => _AppElevatedButtonState();
}

class _AppElevatedButtonState extends State<AppElevatedButton> {
  late bool loading;

  @override
  void initState() {
    loading = false;
    super.initState();
  }

  press() async {
    if (widget.onPressed == null) return;
    if (widget.onPressed is! Future Function()) {
      return widget.onPressed!();
    }

    setState(() {
      loading = true;
    });

    try {
      await (widget.onPressed as Future Function()).call();
    } catch (_) {}

    setState(() {
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: loading
          ? () {}
          : widget.onPressed == null
              ? null
              : press,
      style: ButtonStyle(
        backgroundColor: widget.backgroundColor != null
            ? WidgetStatePropertyAll(widget.backgroundColor!)
            : null,
        foregroundColor: widget.foregroundColor != null
            ? WidgetStatePropertyAll(widget.foregroundColor!)
            : null,
      ),
      child: loading
          ? SizedBox(
              height: 25,
              width: 25,
              child: CircularProgressIndicator(
                color: Theme.of(context)
                    .elevatedButtonTheme
                    .style!
                    .foregroundColor!
                    .resolve({}),
              ),
            )
          : widget.child,
    );
  }
}
