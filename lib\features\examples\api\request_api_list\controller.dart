import 'package:get/get.dart';
import 'package:tredo/core/services/state_management/obs.dart';

import '../../../../core/services/rest_api/rest_api.dart';
import 'models/post.dart';

class RequestAPIListExamplePageController extends GetxController {
  Request<PostModel> postsRequest = Request<PostModel>(
    endPoint: "https://jsonplaceholder.typicode.com/posts",
    isFullURL: true,
    fromJson: PostModel.fromJson,
  );

  ObsList<PostModel> posts = ObsList([]);
  fetchData() async {
    ResponseModel response = await APIService.instance.requestAPI(postsRequest);
    if (response.success) {
      posts.value = response.data;
    } else {
      posts.error = response.message;
    }
  }

  @override
  void onInit() {
    //NOTE - you don't need this
    // it will be injected by default at the start of app
    Get.put(
      APIService(headers: {"Accept": "application/json"}, withLog: false),
    );

    fetchData();
    super.onInit();
  }

  @override
  void onClose() {
    postsRequest.stop();
    super.onClose();
  }
}
