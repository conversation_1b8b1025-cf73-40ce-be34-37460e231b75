import 'package:flutter/widgets.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/image.dart';

class GalleryTab extends StatefulWidget {
  final List<String> images;
  const GalleryTab({super.key, required this.images});

  @override
  State<GalleryTab> createState() => _GalleryTabState();
}

class _GalleryTabState extends State<GalleryTab> {
  int currentImage = 0;

  PageController pageController = PageController(viewportFraction: .8);

  @override
  Widget build(BuildContext context) {
    return SliverList(
      delegate: SliverChildListDelegate([
        SizedBox(
          height: 360,
          child: PageView.builder(
            itemCount: widget.images.length,
            controller: pageController,
            itemBuilder:
                (context, index) => Column(
                  children: [
                    Center(
                      child: AppImage(
                        path: widget.images[index],
                        height: 300,
                        decoration: BoxDecoration(
                          color: context.appColorScheme.primaryColor,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(
                            color: context.appColorScheme.primaryColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
          ),
        ),
      ]),
    );
  }
}
