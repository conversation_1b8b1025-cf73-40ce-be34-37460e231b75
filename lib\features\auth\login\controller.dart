import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/config/app_builder.dart';
import 'package:tredo/core/config/role.dart';

import '../../../core/routes/navigation.dart';

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  late TextEditingController phone, password;

  @override
  void onInit() {
    phone = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    phone.dispose();
    password.dispose();
    super.onClose();
  }

  Future<void> login() async {
    appBuilder.setRole(const User());

    await appBuilder.role.initialize();
    Nav.to(appBuilder.role.landing);
  }

  Future<void> loginAsGuest() async {
    appBuilder.setRole(const Guest());

    await appBuilder.role.initialize();
    Nav.to(appBuilder.role.landing);
  }

  void register() {
    Nav.to(Pages.register);
  }

  void forgetPassword() {
    Nav.to(Pages.forget_password);
  }
}
