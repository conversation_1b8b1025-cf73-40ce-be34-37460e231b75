import 'package:flutter/material.dart';
import 'package:tredo/core/widgets/general/buttons/back_button.dart';

class AuthAppBar extends StatelessWidget {
  final Widget title;
  final Widget? subtitle;
  const AuthAppBar({super.key, required this.title, this.subtitle});

  @override
  Widget build(BuildContext context) {
    final ModalRoute<dynamic>? parentRoute = ModalRoute.of(context);
    bool canPop() => parentRoute?.impliesAppBarDismissal ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16,
        children: [
          SizedBox(
            height: MediaQuery.paddingOf(context).top,
            width: double.infinity,
          ),
          if (canPop())
            const Align(
              alignment: AlignmentDirectional.centerStart,
              child: AppBackButton(withBorder: true),
            ),
          DefaultTextStyle(
            style: Theme.of(
              context,
            ).textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.bold),
            child: title,
          ),
          if (subtitle != null)
            DefaultTextStyle(
              style: Theme.of(context).textTheme.titleMedium!,
              child: subtitle!,
            ),
        ],
      ),
    );
  }
}
