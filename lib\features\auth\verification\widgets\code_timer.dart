import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/utils/num_utils.dart';

import '../controller.dart';

class CodeTimer extends GetView<VerificationPageController> {
  const CodeTimer({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(tr(LocaleKeys.didnt_receive_code)),
              const Gap(8),
              if (controller.secondsRemaining > 0)
                Text(
                  "(${controller.secondsRemaining.formatSeconds})",
                  style: TextStyle(
                    color: context.appColorScheme.secondaryColor,
                  ),
                ),
            ],
          ),
          const Gap(4),
          TextButton(
            onPressed:
                controller.secondsRemaining > 0 ? null : controller.resend,
            child: Text(LocaleKeys.resend_code.tr()),
          ),
        ],
      );
    });
  }
}
