// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:get/get.dart';
// import 'package:renva/core/localization/strings.dart';
// import 'package:renva/core/style/assets/gen/assets.gen.dart';
// import 'package:renva/core/style/style.dart';
// import 'package:renva/features/widgets/general/button.dart';

// enum ConfirmationStatus { confirm, delete }

// class ConfirmationDialog extends StatelessWidget {
//   final ConfirmationStatus status;
//   final Widget? title;
//   final Widget? body;
//   final Function() onConfirm;
//   final Function? onCancel;
//   const ConfirmationDialog({
//     super.key,
//     this.status = ConfirmationStatus.confirm,
//     this.title,
//     this.body,
//     required this.onConfirm,
//     this.onCancel,
//   });

//   static show({
//     ConfirmationStatus status = ConfirmationStatus.confirm,
//     Widget? title,
//     Widget? body,
//     required Function() onConfirm,
//     Function? onCancel,
//   }) =>
//       Get.dialog(ConfirmationDialog(
//         status: status,
//         onConfirm: onConfirm,
//         title: title,
//         body: body,
//         onCancel: onCancel,
//       ));

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           const Gap(12),
//           if (title != null) title!,
//           if (title == null && status == ConfirmationStatus.delete)
//             Assets.icons.deleteIcon.svg(),
//           const Gap(12),
//           if (body != null)
//             DefaultTextStyle(style: context.textTheme.bodyLarge!, child: body!),
//           const Gap(12),
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 16),
//             child: Row(
//               children: [
//                 Expanded(
//                   child: AppElevatedButton(
//                     onPressed: () {
//                       Get.back();
//                       onCancel?.call();
//                     },
//                     backgroundColor: context.appColorScheme.disabledColor,
//                     child: Text(tr(LocaleKeys.cancel)),
//                   ),
//                 ),
//                 const Gap(12),
//                 Expanded(
//                   child: AppElevatedButton(
//                     onPressed: () {
//                       Get.back();
//                       onConfirm();
//                     },
//                     backgroundColor: status == ConfirmationStatus.delete
//                         ? context.appColorScheme.errorColor
//                         : null,
//                     child: Text(tr(LocaleKeys.confirm)),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
