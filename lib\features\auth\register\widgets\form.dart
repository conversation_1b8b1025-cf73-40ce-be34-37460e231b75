import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/fields/password.dart';
import 'package:tredo/core/widgets/general/fields/phone.dart';
import 'package:tredo/core/widgets/general/fields/prefix.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

import '../controller.dart';

class RegisterFormWidget extends GetView<RegisterPageController> {
  const RegisterFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.formKey,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: controller.firstName,
                  validator: Validator.notNull,
                  textInputAction: TextInputAction.next,
                  decoration: InputDecoration(
                    hintText: tr(LocaleKeys.first_name),
                    prefixIcon: FieldPrefix(
                      child: SvgIcon(Assets.icons.person),
                    ),
                  ),
                ),
              ),
              const Gap(8),
              Expanded(
                child: TextFormField(
                  controller: controller.lastName,
                  validator: Validator.notNull,
                  textInputAction: TextInputAction.next,
                  decoration: InputDecoration(
                    hintText: tr(LocaleKeys.last_name),
                    prefixIcon: FieldPrefix(
                      child: SvgIcon(Assets.icons.person),
                    ),
                  ),
                ),
              ),
            ],
          ),

          const Gap(24),
          PhoneField(controller: controller.phone),
          const Gap(24),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField(
                  items:
                      ["Damascus", "Reif "]
                          .map(
                            (e) => DropdownMenuItem(value: e, child: Text(e)),
                          )
                          .toList(),
                  validator: Validator.notNull,
                  onChanged: (value) => log(value.toString()),
                  decoration: InputDecoration(
                    hintText: tr(LocaleKeys.state),
                    prefixIcon: FieldPrefix(child: SvgIcon(Assets.icons.earth)),
                  ),
                ),
              ),
              const Gap(8),
              Expanded(
                child: DropdownButtonFormField(
                  items:
                      ["Damascus", "Yabrud", "Al-Nabek"]
                          .map(
                            (e) => DropdownMenuItem(value: e, child: Text(e)),
                          )
                          .toList(),
                  onChanged: (value) => log(value.toString()),
                  validator: Validator.notNull,
                  decoration: InputDecoration(
                    hintText: tr(LocaleKeys.city),
                    prefixIcon: FieldPrefix(
                      child: SvgIcon(Assets.icons.building),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Gap(24),
          PasswordField(controller: controller.password),
          const Gap(24),
          PasswordField(
            controller: controller.confirmPassword,
            validator:
                (value) =>
                    Validator.confirmPassword(value!, controller.password.text),
            hintText: tr(LocaleKeys.confirm_password),
          ),
        ],
      ),
    );
  }
}
