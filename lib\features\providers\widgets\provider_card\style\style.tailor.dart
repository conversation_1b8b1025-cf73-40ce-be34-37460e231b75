// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'style.dart';

// **************************************************************************
// TailorAnnotationsGenerator
// **************************************************************************

mixin _$ProviderCardThemeTailorMixin on ThemeExtension<ProviderCardTheme> {
  Color get backgroundColor;
  BorderSide get side;
  TextStyle get nameStyle;
  TextStyle get viewsStyle;

  @override
  ProviderCardTheme copyWith({
    Color? backgroundColor,
    BorderSide? side,
    TextStyle? nameStyle,
    TextStyle? viewsStyle,
  }) {
    return ProviderCardTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      side: side ?? this.side,
      nameStyle: nameStyle ?? this.nameStyle,
      viewsStyle: viewsStyle ?? this.viewsStyle,
    );
  }

  @override
  ProviderCardTheme lerp(
    covariant ThemeExtension<ProviderCardTheme>? other,
    double t,
  ) {
    if (other is! ProviderCardTheme) return this as ProviderCardTheme;
    return ProviderCardTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t)!,
      side: t < 0.5 ? side : other.side,
      nameStyle: TextStyle.lerp(nameStyle, other.nameStyle, t)!,
      viewsStyle: TextStyle.lerp(viewsStyle, other.viewsStyle, t)!,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProviderCardTheme &&
            const DeepCollectionEquality().equals(
              backgroundColor,
              other.backgroundColor,
            ) &&
            const DeepCollectionEquality().equals(side, other.side) &&
            const DeepCollectionEquality().equals(nameStyle, other.nameStyle) &&
            const DeepCollectionEquality().equals(
              viewsStyle,
              other.viewsStyle,
            ));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(backgroundColor),
      const DeepCollectionEquality().hash(side),
      const DeepCollectionEquality().hash(nameStyle),
      const DeepCollectionEquality().hash(viewsStyle),
    );
  }
}

extension ProviderCardThemeBuildContextProps on BuildContext {
  ProviderCardTheme get providerCardTheme =>
      Theme.of(this).extension<ProviderCardTheme>()!;
  Color get backgroundColor => providerCardTheme.backgroundColor;
  BorderSide get side => providerCardTheme.side;
  TextStyle get nameStyle => providerCardTheme.nameStyle;
  TextStyle get viewsStyle => providerCardTheme.viewsStyle;
}
