import 'package:flutter/material.dart';

enum AppLocalization {
  en,
  ar;

  static AppLocalization fromString(String s) => switch (s) {
        "en" => AppLocalization.en,
        "ar" => AppLocalization.ar,
        _ => throw Exception("not valid locale"),
      };

  String get value => switch (this) {
        AppLocalization.en => 'en',
        AppLocalization.ar => 'ar',
      };

  // String get text {
  //   switch (this) {
  //     case AppLocalization.ar:
  //       return LocaleKeys.arabic.tr();
  //     case AppLocalization.en:
  //       return LocaleKeys.english.tr();
  //   }
  // }

  String get flagEmoji => switch (this) {
        en => '\u{1F1FA}\u{1F1F8}',
        ar => '\u{1F1FA}\u{1F1F8}',
      };

  bool get isEnglish => this == AppLocalization.en;
  bool get isArabic => this == AppLocalization.ar;

  Locale get locale => switch (this) {
        AppLocalization.en => const Locale('en'),
        AppLocalization.ar => const Locale('ar'),
      };
}
