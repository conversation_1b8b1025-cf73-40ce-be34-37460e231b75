import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'style/style.dart';

class EntityCard extends StatelessWidget {
  const EntityCard({super.key});

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.entityCardTheme;
    return Container(
      width: MediaQuery.sizeOf(context).width * .5,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: cardTheme.backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconTheme(
        data: IconTheme.of(context).copyWith(color: cardTheme.iconColor),
        child: Row(
          children: [
            SvgIcon(Assets.icons.earth, size: 24),
            const Gap(12),
            Expanded(
              child: Text(
                "Damascus University",
                style: cardTheme.style,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Gap(12),
            SvgIcon(Assets.icons.phoneCircle, size: 22),
          ],
        ),
      ),
    );
  }
}
