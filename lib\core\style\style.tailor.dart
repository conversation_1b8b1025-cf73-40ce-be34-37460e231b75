// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'style.dart';

// **************************************************************************
// TailorAnnotationsGenerator
// **************************************************************************

mixin _$AppColorSchemeTailorMixin on ThemeExtension<AppColorScheme> {
  MaterialColor get primaryColor;
  Color get onPrimaryColor;
  MaterialColor get secondaryColor;
  Color get errorColor;
  Color get onErrorColor;
  Color get disabledColor;
  Color get successColor;

  @override
  AppColorScheme copyWith({
    MaterialColor? primaryColor,
    Color? onPrimaryColor,
    MaterialColor? secondaryColor,
    Color? errorColor,
    Color? onErrorColor,
    Color? disabledColor,
    Color? successColor,
  }) {
    return AppColorScheme(
      primaryColor: primaryColor ?? this.primaryColor,
      onPrimaryColor: onPrimaryColor ?? this.onPrimaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      errorColor: errorColor ?? this.errorColor,
      onErrorColor: onErrorColor ?? this.onErrorColor,
      disabledColor: disabledColor ?? this.disabledColor,
      successColor: successColor ?? this.successColor,
    );
  }

  @override
  AppColorScheme lerp(
    covariant ThemeExtension<AppColorScheme>? other,
    double t,
  ) {
    if (other is! AppColorScheme) return this as AppColorScheme;
    return AppColorScheme(
      primaryColor: t < 0.5 ? primaryColor : other.primaryColor,
      onPrimaryColor: Color.lerp(onPrimaryColor, other.onPrimaryColor, t)!,
      secondaryColor: t < 0.5 ? secondaryColor : other.secondaryColor,
      errorColor: Color.lerp(errorColor, other.errorColor, t)!,
      onErrorColor: Color.lerp(onErrorColor, other.onErrorColor, t)!,
      disabledColor: Color.lerp(disabledColor, other.disabledColor, t)!,
      successColor: Color.lerp(successColor, other.successColor, t)!,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppColorScheme &&
            const DeepCollectionEquality().equals(
              primaryColor,
              other.primaryColor,
            ) &&
            const DeepCollectionEquality().equals(
              onPrimaryColor,
              other.onPrimaryColor,
            ) &&
            const DeepCollectionEquality().equals(
              secondaryColor,
              other.secondaryColor,
            ) &&
            const DeepCollectionEquality().equals(
              errorColor,
              other.errorColor,
            ) &&
            const DeepCollectionEquality().equals(
              onErrorColor,
              other.onErrorColor,
            ) &&
            const DeepCollectionEquality().equals(
              disabledColor,
              other.disabledColor,
            ) &&
            const DeepCollectionEquality().equals(
              successColor,
              other.successColor,
            ));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(primaryColor),
      const DeepCollectionEquality().hash(onPrimaryColor),
      const DeepCollectionEquality().hash(secondaryColor),
      const DeepCollectionEquality().hash(errorColor),
      const DeepCollectionEquality().hash(onErrorColor),
      const DeepCollectionEquality().hash(disabledColor),
      const DeepCollectionEquality().hash(successColor),
    );
  }
}

extension AppColorSchemeBuildContextProps on BuildContext {
  AppColorScheme get appColorScheme =>
      Theme.of(this).extension<AppColorScheme>()!;
  MaterialColor get primaryColor => appColorScheme.primaryColor;
  Color get onPrimaryColor => appColorScheme.onPrimaryColor;
  MaterialColor get secondaryColor => appColorScheme.secondaryColor;
  Color get errorColor => appColorScheme.errorColor;
  Color get onErrorColor => appColorScheme.onErrorColor;
  Color get disabledColor => appColorScheme.disabledColor;
  Color get successColor => appColorScheme.successColor;
}

mixin _$PinputThemeTailorMixin on ThemeExtension<PinputTheme> {
  PinTheme get defaultTheme;
  PinTheme get focused;
  PinTheme get submitted;
  PinTheme get error;

  @override
  PinputTheme copyWith({
    PinTheme? defaultTheme,
    PinTheme? focused,
    PinTheme? submitted,
    PinTheme? error,
  }) {
    return PinputTheme(
      defaultTheme: defaultTheme ?? this.defaultTheme,
      focused: focused ?? this.focused,
      submitted: submitted ?? this.submitted,
      error: error ?? this.error,
    );
  }

  @override
  PinputTheme lerp(covariant ThemeExtension<PinputTheme>? other, double t) {
    if (other is! PinputTheme) return this as PinputTheme;
    return PinputTheme(
      defaultTheme: t < 0.5 ? defaultTheme : other.defaultTheme,
      focused: t < 0.5 ? focused : other.focused,
      submitted: t < 0.5 ? submitted : other.submitted,
      error: t < 0.5 ? error : other.error,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PinputTheme &&
            const DeepCollectionEquality().equals(
              defaultTheme,
              other.defaultTheme,
            ) &&
            const DeepCollectionEquality().equals(focused, other.focused) &&
            const DeepCollectionEquality().equals(submitted, other.submitted) &&
            const DeepCollectionEquality().equals(error, other.error));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(defaultTheme),
      const DeepCollectionEquality().hash(focused),
      const DeepCollectionEquality().hash(submitted),
      const DeepCollectionEquality().hash(error),
    );
  }
}

extension PinputThemeBuildContextProps on BuildContext {
  PinputTheme get pinputTheme => Theme.of(this).extension<PinputTheme>()!;
  PinTheme get defaultTheme => pinputTheme.defaultTheme;
  PinTheme get focused => pinputTheme.focused;
  PinTheme get submitted => pinputTheme.submitted;
  PinTheme get error => pinputTheme.error;
}
