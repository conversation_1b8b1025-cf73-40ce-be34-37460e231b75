import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import '../widgets/appbar.dart';
import 'controller.dart';
import 'widgets/form.dart';

class RegisterPage extends GetView<RegisterPageController> {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AuthAppBar(
        title: Text(tr(LocaleKeys.sign_up_to_create_an_account)),
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        children: [
          const RegisterFormWidget(),
          const Gap(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Obx(
                () => Checkbox(
                  value: controller.isTermsAccepted,
                  fillColor: WidgetStateColor.resolveWith((states) {
                    if (states.contains(WidgetState.selected)) {
                      return context.appColorScheme.successColor;
                    }
                    return Colors.transparent;
                  }),
                  onChanged: (value) => controller.isTermsAccepted = value!,
                ),
              ),
              Text(tr(LocaleKeys.i_agree_with)),
              TextButton(
                onPressed: controller.openTerms,
                child: Text(
                  tr(LocaleKeys.terms_and_policies),
                  style: TextStyle(
                    color: context.appColorScheme.secondaryColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
          const Gap(16),
          Obx(
            () => AppElevatedButton(
              onPressed:
                  controller.isTermsAccepted ? controller.register : null,
              child: Text(tr(LocaleKeys.sign_up)),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(tr(LocaleKeys.already_have_an_account)),
              TextButton(
                onPressed: controller.login,
                child: Text(tr(LocaleKeys.login_now)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
