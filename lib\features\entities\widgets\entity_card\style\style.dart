// ignore_for_file: annotate_overrides

import 'package:flutter/material.dart';
import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';
import 'package:tredo/core/style/style.dart';

part 'style.tailor.dart';

@TailorMixin()
class EntityCardTheme extends ThemeExtension<EntityCardTheme>
    with _$EntityCardThemeTailorMixin {
  EntityCardTheme({
    required this.backgroundColor,
    required this.iconColor,
    required this.style,
  });
  final Color backgroundColor;
  final Color iconColor;
  final TextStyle style;

  factory EntityCardTheme.light(
    AppColorScheme colorScheme,
    TextTheme textTheme,
  ) => EntityCardTheme(
    backgroundColor: colorScheme.primaryColor,
    iconColor: colorScheme.primaryColor.shade200,
    style: textTheme.bodyMedium!.copyWith(
      color: colorScheme.onPrimaryColor,
      fontWeight: FontWeight.w500,
    ),
  );
}
