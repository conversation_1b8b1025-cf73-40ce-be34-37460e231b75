// import 'package:day_night_time_picker/day_night_time_picker.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:renva/core/utils/date.dart' as dateutils;

// class TimeOfDayFormField extends FormField<int> {
//   final String? hintText;
//   final Function(int? value)? onChanged;

//   TimeOfDayFormField({
//     this.hintText,
//     this.onChanged,
//     super.initialValue,
//     super.validator,
//     super.key,
//   }) : super(
//           builder: (field) {
//             final effectiveDecoration =
//                 Theme.of(field.context).inputDecorationTheme;

//             return UnmanagedRestorationScope(
//               bucket: field.bucket,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   InkWell(
//                     borderRadius:
//                         effectiveDecoration.border is OutlineInputBorder
//                             ? (effectiveDecoration.border as OutlineInputBorder)
//                                 .borderRadius
//                             : null,
//                     onTap: () async {
//                       dynamic time = await Navigator.push(
//                         field.context,
//                         showPicker(
//                           context: field.context,
//                           value: Time(hour: 0, minute: 0),
//                           onChange: (_) {},
//                         ),
//                       );

//                       if (time == null && time! is TimeOfDay) return;

//                       time = time as TimeOfDay;

//                       int value = time.hour * 60 + time.minute;

//                       field.didChange(value);
//                       onChanged?.call(value);
//                     },
//                     child: Container(
//                       height: 50,
//                       padding: const EdgeInsets.symmetric(horizontal: 16),
//                       alignment: AlignmentDirectional.centerStart,
//                       decoration: BoxDecoration(
//                         border: Border.fromBorderSide(
//                           field.hasError
//                               ? effectiveDecoration.errorBorder!.borderSide
//                               : effectiveDecoration.border!.borderSide,
//                         ),
//                         borderRadius: effectiveDecoration.border
//                                 is OutlineInputBorder
//                             ? (effectiveDecoration.border as OutlineInputBorder)
//                                 .borderRadius
//                             : null,
//                       ),
//                       child: (field.value == null)
//                           ? Text(
//                               hintText ?? "",
//                               style: effectiveDecoration.hintStyle,
//                             )
//                           : Text(dateutils.DateUtils.createTimeOfDayFromMinutes(
//                                   field.value!)
//                               .format(field.context)),
//                     ),
//                   ),
//                   if (field.hasError) const Gap(8),
//                   if (field.hasError)
//                     Text(
//                       field.errorText ?? "",
//                       style: effectiveDecoration.errorStyle,
//                     ),
//                 ],
//               ),
//             );
//           },
//         );
// }
