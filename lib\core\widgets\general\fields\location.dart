// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:renva/features/usecases/choose_address/index.dart';
// import 'package:renva/features/usecases/choose_address/models/result.dart';

// class LocationFormField extends FormField<DetailedLocation> {
//   final String? hintText;
//   final Function(DetailedLocation? value)? onChanged;

//   LocationFormField({
//     this.hintText,
//     this.onChanged,
//     super.initialValue,
//     super.validator,
//     super.key,
//   }) : super(
//           builder: (field) {
//             final effectiveDecoration =
//                 Theme.of(field.context).inputDecorationTheme;

//             return UnmanagedRestorationScope(
//               bucket: field.bucket,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   InkWell(
//                     borderRadius:
//                         effectiveDecoration.border is OutlineInputBorder
//                             ? (effectiveDecoration.border as OutlineInputBorder)
//                                 .borderRadius
//                             : null,
//                     onTap: () async {
//                       DetailedLocation? location = await AddressPicker.pick();

//                       if (location == null) return;

//                       field.didChange(location);
//                       onChanged?.call(location);
//                     },
//                     child: Container(
//                       height: 50,
//                       padding: const EdgeInsets.symmetric(horizontal: 16),
//                       alignment: AlignmentDirectional.centerStart,
//                       decoration: BoxDecoration(
//                         border: Border.fromBorderSide(
//                           field.hasError
//                               ? effectiveDecoration.errorBorder!.borderSide
//                               : effectiveDecoration.border!.borderSide,
//                         ),
//                         borderRadius: effectiveDecoration.border
//                                 is OutlineInputBorder
//                             ? (effectiveDecoration.border as OutlineInputBorder)
//                                 .borderRadius
//                             : null,
//                       ),
//                       child: (field.value == null)
//                           ? Text(
//                               hintText ?? "",
//                               style: effectiveDecoration.hintStyle,
//                             )
//                           : Text(field.value!.description),
//                     ),
//                   ),
//                   if (field.hasError) const Gap(8),
//                   if (field.hasError)
//                     Text(
//                       field.errorText ?? "",
//                       style: effectiveDecoration.errorStyle,
//                     ),
//                 ],
//               ),
//             );
//           },
//         );
// }
