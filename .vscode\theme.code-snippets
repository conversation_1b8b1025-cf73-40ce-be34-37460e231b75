{
	"theme": {
		"prefix": "theme extension",
		"body": [
			"@TailorMixin()",
			"class $1 extends ThemeExtension<$1>",
			"    with _$$1TailorMixin {",
			"  $1();",
			"}",
		]
	},

	"theme_full": {
		"prefix": "theme extension with imports",
		"body": [
			"// ignore_for_file: annotate_overrides",
			"",
			"import 'package:flutter/material.dart';",
			"import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';",
			"",
			"part 'style.tailor.dart';",
			"",
			"@TailorMixin()",
			"class $1 extends ThemeExtension<$1>",
			"    with _$$1TailorMixin {",
			"  $1();",
			"}",
		]
	},
}