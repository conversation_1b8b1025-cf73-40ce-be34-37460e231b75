import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/core/widgets/general/fields/password.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import '../widgets/appbar.dart';
import 'controller.dart';
import 'models/nav.dart';

class ResetPasswordPage extends GetView<ResetPasswordPageController> {
  const ResetPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    bool isResetMode = controller.nav.mode == ResetPasswordMode.reset;

    return AppScaffold(
      appBar: AuthAppBar(
        title: Text(
          tr(
            isResetMode
                ? LocaleKeys.reset_password
                : LocaleKeys.change_password,
          ),
        ),
      ),
      body: Form(
        key: controller.formKey,
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          children: [
            PasswordField(
              controller: controller.firstPassword,
              hintText: tr(
                isResetMode ? LocaleKeys.new_password : LocaleKeys.old_password,
              ),
            ),
            const Gap(36),
            PasswordField(
              controller: controller.secondPassword,
              validator:
                  isResetMode
                      ? (value) => Validator.confirmPassword(
                        value!,
                        controller.firstPassword.text,
                      )
                      : null,
              hintText: tr(
                isResetMode
                    ? LocaleKeys.confirm_password
                    : LocaleKeys.new_password,
              ),
            ),
            const Gap(36),
            AppElevatedButton(
              onPressed: controller.resetPassword,
              child: Text(tr(LocaleKeys.reset_password)),
            ),
          ],
        ),
      ),
    );
  }
}
