import 'package:flutter/material.dart';

class TagsRow extends StatelessWidget {
  final List<String> tags;
  const TagsRow({super.key, required this.tags});

  static const rowSpacing = 6.0;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double availableWidth = constraints.maxWidth;
        double usedWidth = 0.0;

        List<Widget> visibleChips = [];

        for (String tag in tags) {
          // Estimate chip width
          final TextPainter textPainter = TextPainter(
            text: TextSpan(text: tag, style: ChipTheme.of(context).labelStyle),
            maxLines: 1,
            textDirection: TextDirection.ltr,
          )..layout();

          double chipWidth = textPainter.width + 18 + rowSpacing;

          if (usedWidth + chipWidth < availableWidth) {
            usedWidth += chipWidth;
            visibleChips.add(Chip(label: Text(tag)));
          } else {
            break;
          }
        }

        return Row(spacing: rowSpacing, children: visibleChips);
      },
    );
  }
}
