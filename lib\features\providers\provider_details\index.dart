import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/demo/media.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/core/widgets/general/tab_bar.dart';
import 'package:tredo/features/categories/widgets/category_card/style/style.dart';

import 'widgets/app_bar.dart';
import 'controller.dart';
import 'widgets/tabs/details.dart';
import 'widgets/tabs/gallery.dart';
import 'widgets/tabs/offers.dart';

class ProviderDetailsPage extends GetView<ProviderDetailsPageController> {
  const ProviderDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final categoryCardTheme = context.categoryCardTheme;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          const ProviderAppBar(),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                Text("Mohammad Enawe", style: context.textTheme.headlineSmall),
                const Gap(8),
                Row(
                  children: [
                    Container(
                      height: 35,
                      width: 35,
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: categoryCardTheme.backgroundColor,
                        shape: BoxShape.circle,
                      ),
                      child: SvgIcon(
                        Assets.icons.earth,
                        color: categoryCardTheme.iconColor,
                      ),
                    ),
                    const Gap(8),
                    Expanded(
                      child: Text(
                        "Category Name",
                        style: categoryCardTheme.style,
                      ),
                    ),
                  ],
                ),
                const Gap(8),
                Row(
                  spacing: 16,
                  children: [
                    Row(
                      spacing: 8,
                      children: [
                        SvgIcon(
                          Assets.icons.earth,
                          color: context.appColorScheme.secondaryColor,
                        ),
                        const Text("Reif-Dimashq"),
                      ],
                    ),
                    Row(
                      spacing: 8,
                      children: [
                        SvgIcon(
                          Assets.icons.building,
                          color: context.appColorScheme.secondaryColor,
                        ),
                        const Text("Yabrud"),
                      ],
                    ),
                  ],
                ),
              ]),
            ),
          ),
          const SliverGap(16),
          PinnedHeaderSliver(
            child: ColoredBox(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: AppTabBar(
                  onChanged: (index) => controller.currentTabIndex = index,
                  items: [
                    Text(tr(LocaleKeys.details)),
                    Text(tr(LocaleKeys.gallery)),
                    Text(tr(LocaleKeys.offers)),
                  ],
                ),
              ),
            ),
          ),
          Obx(
            () => switch (controller.currentTabIndex) {
              0 => const DetailsTab(),
              1 => GalleryTab(
                images: [
                  DemoMedia.getAppRandomImage,
                  DemoMedia.getAppRandomImage,
                  DemoMedia.getAppRandomImage,
                  DemoMedia.getAppRandomImage,
                  DemoMedia.getAppRandomImage,
                ],
              ),
              2 => const OffersTab(),
              _ => SliverList(delegate: SliverChildListDelegate([])),
            },
          ),
        ],
      ),
    );
  }
}
