import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tredo/core/assets/assets.gen.dart';

export 'package:tredo/core/assets/assets.gen.dart';

class SvgIcon extends StatelessWidget {
  final SvgGenImage svg;
  final Color? color;
  final double? size;
  const SvgIcon(this.svg, {super.key, this.color, this.size});

  @override
  Widget build(BuildContext context) {
    Color? effectiveColor = color ?? IconTheme.of(context).color;
    return SvgPicture.asset(
      svg.path,
      height: size,
      width: size,
      colorFilter:
          effectiveColor == null
              ? null
              : ColorFilter.mode(effectiveColor, BlendMode.srcATop),
    );
  }
}

class SvgIconString extends StatelessWidget {
  final String svg;
  final Color? color;
  final double? size;
  const SvgIconString(this.svg, {super.key, this.color, this.size});

  @override
  Widget build(BuildContext context) {
    Color? effectiveColor = color ?? IconTheme.of(context).color;
    return SvgPicture.string(
      svg,
      height: size,
      width: size,
      colorFilter:
          effectiveColor == null
              ? null
              : ColorFilter.mode(effectiveColor, BlendMode.srcATop),
    );
  }
}
