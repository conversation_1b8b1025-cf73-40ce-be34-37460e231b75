// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'style.dart';

// **************************************************************************
// TailorAnnotationsGenerator
// **************************************************************************

mixin _$EntityCardThemeTailorMixin on ThemeExtension<EntityCardTheme> {
  Color get backgroundColor;
  Color get iconColor;
  TextStyle get style;

  @override
  EntityCardTheme copyWith({
    Color? backgroundColor,
    Color? iconColor,
    TextStyle? style,
  }) {
    return EntityCardTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      iconColor: iconColor ?? this.iconColor,
      style: style ?? this.style,
    );
  }

  @override
  EntityCardTheme lerp(
    covariant ThemeExtension<EntityCardTheme>? other,
    double t,
  ) {
    if (other is! EntityCardTheme) return this as EntityCardTheme;
    return EntityCardTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t)!,
      iconColor: Color.lerp(iconColor, other.iconColor, t)!,
      style: TextStyle.lerp(style, other.style, t)!,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EntityCardTheme &&
            const DeepCollectionEquality().equals(
              backgroundColor,
              other.backgroundColor,
            ) &&
            const DeepCollectionEquality().equals(iconColor, other.iconColor) &&
            const DeepCollectionEquality().equals(style, other.style));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(backgroundColor),
      const DeepCollectionEquality().hash(iconColor),
      const DeepCollectionEquality().hash(style),
    );
  }
}

extension EntityCardThemeBuildContextProps on BuildContext {
  EntityCardTheme get entityCardTheme =>
      Theme.of(this).extension<EntityCardTheme>()!;
  Color get backgroundColor => entityCardTheme.backgroundColor;
  Color get iconColor => entityCardTheme.iconColor;
  TextStyle get style => entityCardTheme.style;
}
