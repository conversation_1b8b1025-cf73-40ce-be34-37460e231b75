import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class DateFormField extends FormField<DateTime> {
  final DateTime firstDate;
  final DateTime lastDate;
  final String? hintText;
  final Function(DateTime? value)? onChanged;

  DateFormField({
    required this.firstDate,
    required this.lastDate,
    this.hintText,
    this.onChanged,
    super.initialValue,
    super.validator,
    super.key,
  }) : super(
          builder: (field) {
            final effectiveDecoration =
                Theme.of(field.context).inputDecorationTheme;

            return UnmanagedRestorationScope(
              bucket: field.bucket,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    borderRadius:
                        effectiveDecoration.border is OutlineInputBorder
                            ? (effectiveDecoration.border as OutlineInputBorder)
                                .borderRadius
                            : null,
                    onTap: () async {
                      DateTime? date = await showDatePicker(
                          context: field.context,
                          firstDate: firstDate,
                          lastDate: lastDate);

                      if (date == null) return;

                      field.didChange(date);
                      onChanged?.call(date);
                    },
                    child: Container(
                      height: 50,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      alignment: AlignmentDirectional.centerStart,
                      decoration: BoxDecoration(
                        border: Border.fromBorderSide(
                          field.hasError
                              ? effectiveDecoration.errorBorder!.borderSide
                              : effectiveDecoration.border!.borderSide,
                        ),
                        borderRadius: effectiveDecoration.border
                                is OutlineInputBorder
                            ? (effectiveDecoration.border as OutlineInputBorder)
                                .borderRadius
                            : null,
                      ),
                      child: (field.value == null)
                          ? Text(
                              hintText ?? "",
                              style: effectiveDecoration.hintStyle,
                            )
                          : Text(DateFormat.yMd().format(field.value!)),
                    ),
                  ),
                  if (field.hasError) const Gap(8),
                  if (field.hasError)
                    Text(
                      field.errorText ?? "",
                      style: effectiveDecoration.errorStyle,
                    ),
                ],
              ),
            );
          },
        );
}
