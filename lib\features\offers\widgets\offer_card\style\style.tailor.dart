// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'style.dart';

// **************************************************************************
// TailorAnnotationsGenerator
// **************************************************************************

mixin _$OfferCardThemeTailorMixin on ThemeExtension<OfferCardTheme> {
  Color get providerAvatarBorderColor;
  List<BoxShadow> get shadow;
  Color get providerDataBackgroundColor;
  TextStyle get providerNameStyle;

  @override
  OfferCardTheme copyWith({
    Color? providerAvatarBorderColor,
    List<BoxShadow>? shadow,
    Color? providerDataBackgroundColor,
    TextStyle? providerNameStyle,
  }) {
    return OfferCardTheme(
      providerAvatarBorderColor:
          providerAvatarBorderColor ?? this.providerAvatarBorderColor,
      shadow: shadow ?? this.shadow,
      providerDataBackgroundColor:
          providerDataBackgroundColor ?? this.providerDataBackgroundColor,
      providerNameStyle: providerNameStyle ?? this.providerNameStyle,
    );
  }

  @override
  OfferCardTheme lerp(
    covariant ThemeExtension<OfferCardTheme>? other,
    double t,
  ) {
    if (other is! OfferCardTheme) return this as OfferCardTheme;
    return OfferCardTheme(
      providerAvatarBorderColor:
          Color.lerp(
            providerAvatarBorderColor,
            other.providerAvatarBorderColor,
            t,
          )!,
      shadow: t < 0.5 ? shadow : other.shadow,
      providerDataBackgroundColor:
          Color.lerp(
            providerDataBackgroundColor,
            other.providerDataBackgroundColor,
            t,
          )!,
      providerNameStyle:
          TextStyle.lerp(providerNameStyle, other.providerNameStyle, t)!,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OfferCardTheme &&
            const DeepCollectionEquality().equals(
              providerAvatarBorderColor,
              other.providerAvatarBorderColor,
            ) &&
            const DeepCollectionEquality().equals(shadow, other.shadow) &&
            const DeepCollectionEquality().equals(
              providerDataBackgroundColor,
              other.providerDataBackgroundColor,
            ) &&
            const DeepCollectionEquality().equals(
              providerNameStyle,
              other.providerNameStyle,
            ));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(providerAvatarBorderColor),
      const DeepCollectionEquality().hash(shadow),
      const DeepCollectionEquality().hash(providerDataBackgroundColor),
      const DeepCollectionEquality().hash(providerNameStyle),
    );
  }
}

extension OfferCardThemeBuildContextProps on BuildContext {
  OfferCardTheme get offerCardTheme =>
      Theme.of(this).extension<OfferCardTheme>()!;
  Color get providerAvatarBorderColor =>
      offerCardTheme.providerAvatarBorderColor;
  List<BoxShadow> get shadow => offerCardTheme.shadow;
  Color get providerDataBackgroundColor =>
      offerCardTheme.providerDataBackgroundColor;
  TextStyle get providerNameStyle => offerCardTheme.providerNameStyle;
}
