import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:tredo/core/localization/strings.dart';

class ProvidersAppBar extends StatelessWidget {
  const ProvidersAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.paddingOf(context).top + 100,
      child: Center(
        child: Text(
          tr(LocaleKeys.service_providers),
          style: context.textTheme.headlineSmall,
        ),
      ),
    );
  }
}
