import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/features/entities/widgets/entity_card/entity_card.dart';

class EntitiesList extends StatelessWidget {
  const EntitiesList({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 65,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 10,
        separatorBuilder: (_, __) => const Gap(12),
        itemBuilder: (context, index) => const EntityCard(),
      ),
    );
  }
}
