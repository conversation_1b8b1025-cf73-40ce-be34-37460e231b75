import 'dart:math';

import 'package:get/get.dart';

import '../../../core/services/state_management/obs.dart';

typedef Location = ({double lat, double lng});

class ObsExamplePageController extends GetxController {
  late ObsVar<Location> location;

  getLocation() async {
    try {
      location.value = await LocationAPI.currentLocation();
    } catch (e) {
      location.error = e.toString();
    }
  }

  regetLocation() {
    location.reset();
    getLocation();
  }

  @override
  void onInit() {
    location = ObsVar(null);
    getLocation();
    super.onInit();
  }
}

abstract class LocationAPI {
  static Future<Location> currentLocation() async {
    await 3.seconds.delay();
    if (Random().nextBool()) {
      Location location =
          (lat: Random().nextDouble() * 90, lng: Random().nextDouble() * 90);
      return location;
    } else {
      throw "Can't get the location";
    }
  }
}
