import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/features/entities/index.dart';
import 'package:tredo/features/offers/index.dart';
import 'package:tredo/features/profile/profile/index.dart';
import 'package:tredo/features/providers/providers/index.dart';

import '../home/<USER>';
import 'controller.dart';
import 'models/destination.dart';
import 'widgets/nav_bar.dart';

class MainPage extends GetView<MainPageController> {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // bottomNavigationBar: const BottomNavBar(),
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 40,
            child: Obx(
              () => AnimatedSwitcher(
                duration: 300.milliseconds,
                transitionBuilder:
                    (child, animation) => ScaleTransition(
                      scale: animation,
                      alignment:
                          controller.prevPageIndex > controller.pageIndex
                              ? (Directionality.of(context) == TextDirection.ltr
                                  ? Alignment.centerLeft
                                  : Alignment.centerRight)
                              : (Directionality.of(context) == TextDirection.ltr
                                  ? Alignment.centerRight
                                  : Alignment.centerLeft),
                      child: FadeTransition(opacity: animation, child: child),
                    ),
                child: switch (controller.destination) {
                  HomeDestination.home => const HomePage(),
                  HomeDestination.offers => const OffersPage(),
                  HomeDestination.providers => const ProvidersPage(),
                  HomeDestination.entities => const EntitiesPage(),
                  HomeDestination.settings => const ProfilePage(),
                },
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Blur(
              blurColor: Theme.of(
                context,
              ).scaffoldBackgroundColor.withValues(alpha: .01),
              child: Container(
                height: 80,
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).scaffoldBackgroundColor,
                      Theme.of(
                        context,
                      ).scaffoldBackgroundColor.withValues(alpha: .01),
                    ],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                  ),
                ),
              ),
            ),
          ),
          const Positioned(bottom: 0, left: 0, right: 0, child: BottomNavBar()),
        ],
      ),
    );
  }
}
