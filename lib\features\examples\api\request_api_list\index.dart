import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/widgets/error_widget.dart';

import 'controller.dart';
import 'widgets/post_card.dart';

class RequestAPIListExamplePage extends StatelessWidget {
  const RequestAPIListExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    RequestAPIListExamplePageController controller = Get.put(
      RequestAPIListExamplePageController(),
    );
    return Scaffold(
      appBar: AppBar(title: const Text("Request List Example")),
      body: Obx(() {
        if (controller.posts.loading) {
          return const Center(child: CircularProgressIndicator());
        } else if (controller.posts.hasError) {
          return AppErrorWidget(error: controller.posts.error!);
        } else {
          return ListView.separated(
            itemCount: controller.posts.valueLength,
            separatorBuilder: (_, __) => const Gap(12),
            itemBuilder:
                (context, index) => PostCard(post: controller.posts[index]),
          );
        }
      }),
    );
  }
}
