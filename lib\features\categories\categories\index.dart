import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/widgets/general/buttons/back_button.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/features/categories/widgets/category_card/category_card.dart';

import 'controller.dart';

class CategoriesPage extends GetView<CategoriesPageController> {
  const CategoriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AppBar(title: Text(tr(LocaleKeys.categories))),
      body: AlignedGridView.count(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        crossAxisCount: 4,
        mainAxisSpacing: 16,
        crossAxisSpacing: 12,
        itemBuilder: (context, index) => const CategoryCard(),
      ),
    );
  }
}
