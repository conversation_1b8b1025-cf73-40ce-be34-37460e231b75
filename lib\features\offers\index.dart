import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import 'controller.dart';
import 'widgets/app_bar.dart';
import 'widgets/offer_card/offer_card.dart';

class OffersPage extends GetView<OffersPageController> {
  const OffersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const OffersAppBar(),
      body: GridView.builder(
        padding: const EdgeInsets.only(
          top: 24,
          bottom: 70,
          right: 16,
          left: 16,
        ),
        itemCount: 12,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 12,
          crossAxisSpacing: 12,
          childAspectRatio: OfferCard.aspectRatio,
        ),
        itemBuilder: (context, index) => const OfferCard(),
      ),
    );
  }
}
