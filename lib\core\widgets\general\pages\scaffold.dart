import 'package:flutter/material.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/style/style.dart';

class AppScaffold extends StatelessWidget {
  //AppBar
  final bool? resizeToAvoidBottomInset;
  final Widget appBar;
  final Widget body;
  final Widget? overlay;

  const AppScaffold({
    super.key,
    required this.appBar,
    required this.body,
    this.resizeToAvoidBottomInset,
    this.overlay,
  });

  @override
  Widget build(BuildContext context) {
    AppColorScheme colorScheme = context.appColorScheme;

    return Scaffold(
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      backgroundColor: colorScheme.primaryColor,
      body: Stack(
        children: [
          Assets.images.headerBackground.image(),
          Column(
            children: [
              Theme(
                data: Theme.of(context).copyWith(
                  iconTheme: IconThemeData(color: colorScheme.onPrimaryColor),
                  textTheme: Theme.of(context).textTheme.apply(
                    bodyColor: colorScheme.onPrimaryColor,
                    displayColor: colorScheme.onPrimaryColor,
                  ),
                ),
                child: appBar,
              ),
              Expanded(
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(32),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                    ),
                    child: body,
                  ),
                ),
              ),
            ],
          ),
          if (overlay != null) overlay!,
        ],
      ),
    );
  }
}
