import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../constants/variable_status.dart';
import '../obs.dart';

export '../constants/variable_status.dart';

class ObsVariableBuilder<T> extends StatelessWidget {
  final ObsVar<T> obs;
  final Widget Function(
          BuildContext context, VariableStatus status, String? error, T? value)
      builder;
  const ObsVariableBuilder(
      {super.key, required this.obs, required this.builder});

  @override
  Widget build(BuildContext context) {
    return Obx(() => builder(context, obs.status, obs.error, obs.value));
  }
}

class ObsListBuilder<T> extends StatelessWidget {
  final ObsList<T> obs;
  final Widget Function(BuildContext context, VariableStatus status,
      String? error, int length, List<T>? value) builder;
  const ObsListBuilder({super.key, required this.obs, required this.builder});

  @override
  Widget build(BuildContext context) {
    return Obx(() =>
        builder(context, obs.status, obs.error, obs.valueLength, obs.value));
  }
}
