// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';

// import 'svg_icon.dart';

// class PrefixIcon extends StatelessWidget {
//   final SvgGenImage icon;
//   const PrefixIcon(this.icon, {super.key});

//   @override
//   Widget build(BuildContext context) {
//     return IntrinsicHeight(
//       child: Padding(
//         padding: const EdgeInsets.all(8.0),
//         child: Row(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             const Gap(12),
//             SvgIcon(icon, size: 25),
//             const Gap(12),
//             const VerticalDivider()
//           ],
//         ),
//       ),
//     );
//   }
// }
