import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/core/widgets/general/fields/password.dart';
import 'package:tredo/core/widgets/general/fields/phone.dart';

import '../../../core/widgets/general/pages/scaffold.dart';
import '../widgets/appbar.dart';
import 'controller.dart';

class LoginPage extends GetView<LoginPageController> {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AuthAppBar(
        title: Text(tr(LocaleKeys.login_to_your_account)),
        subtitle: Text(tr(LocaleKeys.welcome_back_login_to_continue)),
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        children: [
          PhoneField(controller: controller.phone),
          const Gap(32),
          PasswordField(controller: controller.password),
          const Gap(32),
          AppElevatedButton(
            onPressed: controller.login,
            child: Text(tr(LocaleKeys.login)),
          ),
          const Gap(8),
          Align(
            alignment: AlignmentDirectional.centerStart,
            child: TextButton(
              onPressed: controller.forgetPassword,
              child: Text(tr(LocaleKeys.forget_password_question)),
            ),
          ),
          const Gap(8),
          Row(
            children: [
              const Expanded(child: Divider()),
              const Gap(8),
              Text(
                tr(LocaleKeys.or),
                style: context.textTheme.titleMedium!.copyWith(
                  color: StyleRepo.darkGrey,
                ),
              ),
              TextButton(
                onPressed: controller.loginAsGuest,
                child: Text(
                  tr(LocaleKeys.enter_as_a_guest),
                  style: context.textTheme.titleMedium!.copyWith(
                    color: context.appColorScheme.primaryColor,
                  ),
                ),
              ),
              const Expanded(child: Divider()),
            ],
          ),
          const Gap(8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(tr(LocaleKeys.dont_have_an_account)),
              TextButton(
                onPressed: controller.register,
                child: Text(tr(LocaleKeys.register_now)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
