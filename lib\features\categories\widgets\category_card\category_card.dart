import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/features/categories/widgets/category_card/style/style.dart';

class CategoryCard extends StatelessWidget {
  const CategoryCard({super.key});

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.categoryCardTheme;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {},
        borderRadius: BorderRadius.circular(5),
        child: Column(
          children: [
            Container(
              height: 60,
              width: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: cardTheme.backgroundColor,
              ),
              child: Center(
                child: SvgIcon(
                  Assets.icons.earth,
                  color: cardTheme.iconColor,
                  size: 35,
                ),
              ),
            ),
            const Gap(8),
            SizedBox(
              width: MediaQuery.sizeOf(context).width * .2,
              child: Text(
                "Category " * 2,
                style: cardTheme.style,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
