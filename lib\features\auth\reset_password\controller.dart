import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'models/nav.dart';

class ResetPasswordPageController extends GetxController {
  final ResetPasswordPageNav nav;
  ResetPasswordPageController(this.nav);

  final formKey = GlobalKey<FormState>();

  late TextEditingController firstPassword, secondPassword;

  @override
  void onInit() {
    firstPassword = TextEditingController();
    secondPassword = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    firstPassword.dispose();
    secondPassword.dispose();
    super.onClose();
  }

  resetPassword() {
    if (!formKey.currentState!.validate()) return;
  }
}
