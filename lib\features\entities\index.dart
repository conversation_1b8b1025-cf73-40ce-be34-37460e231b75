import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import 'controller.dart';
import 'widgets/app_bar.dart';
import 'widgets/entity_card/entity_card.dart';

class EntitiesPage extends GetView<EntitiesPageController> {
  const EntitiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: const EntitiesAppBar(),
      body: AlignedGridView.count(
        padding: const EdgeInsets.only(
          top: 24,
          bottom: 70,
          left: 16,
          right: 16,
        ),
        itemCount: 24,
        crossAxisCount: 2,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        itemBuilder: (context, index) => const EntityCard(),
      ),
    );
  }
}
