// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'style.dart';

// **************************************************************************
// TailorAnnotationsGenerator
// **************************************************************************

mixin _$ProviderTileThemeTailorMixin on ThemeExtension<ProviderTileTheme> {
  BorderSide get side;
  List<BoxShadow> get imageShadow;
  TextStyle get nameStyle;
  TextStyle get viewsStyle;

  @override
  ProviderTileTheme copyWith({
    BorderSide? side,
    List<BoxShadow>? imageShadow,
    TextStyle? nameStyle,
    TextStyle? viewsStyle,
  }) {
    return ProviderTileTheme(
      side: side ?? this.side,
      imageShadow: imageShadow ?? this.imageShadow,
      nameStyle: nameStyle ?? this.nameStyle,
      viewsStyle: viewsStyle ?? this.viewsStyle,
    );
  }

  @override
  ProviderTileTheme lerp(
    covariant ThemeExtension<ProviderTileTheme>? other,
    double t,
  ) {
    if (other is! ProviderTileTheme) return this as ProviderTileTheme;
    return ProviderTileTheme(
      side: t < 0.5 ? side : other.side,
      imageShadow: t < 0.5 ? imageShadow : other.imageShadow,
      nameStyle: TextStyle.lerp(nameStyle, other.nameStyle, t)!,
      viewsStyle: TextStyle.lerp(viewsStyle, other.viewsStyle, t)!,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProviderTileTheme &&
            const DeepCollectionEquality().equals(side, other.side) &&
            const DeepCollectionEquality().equals(
              imageShadow,
              other.imageShadow,
            ) &&
            const DeepCollectionEquality().equals(nameStyle, other.nameStyle) &&
            const DeepCollectionEquality().equals(
              viewsStyle,
              other.viewsStyle,
            ));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(side),
      const DeepCollectionEquality().hash(imageShadow),
      const DeepCollectionEquality().hash(nameStyle),
      const DeepCollectionEquality().hash(viewsStyle),
    );
  }
}

extension ProviderTileThemeBuildContextProps on BuildContext {
  ProviderTileTheme get providerTileTheme =>
      Theme.of(this).extension<ProviderTileTheme>()!;
  BorderSide get side => providerTileTheme.side;
  List<BoxShadow> get imageShadow => providerTileTheme.imageShadow;
  TextStyle get nameStyle => providerTileTheme.nameStyle;
  TextStyle get viewsStyle => providerTileTheme.viewsStyle;
}
