import 'package:easy_localization/easy_localization.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';

enum HomeDestination {
  home,
  offers,
  providers,
  entities,
  settings;

  String get text => switch (this) {
    home => tr(LocaleKeys.home),
    offers => tr(LocaleKeys.offers),
    providers => tr(LocaleKeys.providers),
    entities => tr(LocaleKeys.entities),
    settings => tr(LocaleKeys.settings),
  };

  SvgGenImage get selectedIcon => switch (this) {
    home => Assets.icons.homeFilled,
    offers => Assets.icons.offerFilled,
    providers => Assets.icons.providersFilled,
    entities => Assets.icons.entitiesFilled,
    settings => Assets.icons.settingsFilled,
  };

  SvgGenImage get unselectedIcon => switch (this) {
    home => Assets.icons.homeOutlined,
    offers => Assets.icons.offerOutlined,
    providers => Assets.icons.providersOutlined,
    entities => Assets.icons.entitiesOutlined,
    settings => Assets.icons.settingsOutlined,
  };
}
