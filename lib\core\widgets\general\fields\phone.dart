import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tredo/core/config/defaults.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

import 'prefix.dart';

class PhoneField extends StatelessWidget {
  final TextEditingController? controller;
  final void Function(bool isValid)? onValidityChange;
  final bool showCounter;

  const PhoneField({
    super.key,
    this.controller,
    this.onValidityChange,
    this.showCounter = false,
  });

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: TextFormField(
        controller: controller,
        onChanged: (value) {
          onValidityChange?.call(Validator.phone(value) == null);
        },
        validator: Validator.phone,
        keyboardType: const TextInputType.numberWithOptions(),
        textInputAction: TextInputAction.next,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^[0-9]*$')),
          LengthLimitingTextInputFormatter(10),
        ],
        maxLength: showCounter ? 10 : null,
        decoration: InputDecoration(
          hintText: tr(LocaleKeys.write_your_phone_number),
          prefixIcon: FieldPrefix(
            child: IntrinsicHeight(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                spacing: 8,
                children: [
                  SvgIcon(Assets.icons.phone),
                  const Text(Default.countryCode),
                  const VerticalDivider(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
