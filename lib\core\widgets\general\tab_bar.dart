import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/style/style.dart';

const double _kTabBarHeight = 42.0;

class AppTabBar extends StatefulWidget {
  final List<Widget> items;
  final int initalIndex;
  final void Function(int index)? onChanged;

  const AppTabBar({
    super.key,
    required this.items,
    this.initalIndex = 0,
    this.onChanged,
  });

  @override
  State<AppTabBar> createState() => _AppTabBarState();
}

class _AppTabBarState extends State<AppTabBar> {
  late final Rx<int> _selected;
  int get selected => _selected.value;
  set selected(int value) {
    widget.onChanged?.call(value);
    _selected.value = value;
  }

  @override
  void initState() {
    _selected = widget.initalIndex.obs;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AppTabBar oldWidget) {
    if (oldWidget.initalIndex != widget.initalIndex) {
      selected = widget.initalIndex;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _kTabBarHeight,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final totalSpacing = 12 * (widget.items.length - 1);
          final tabWidth =
              (constraints.maxWidth - totalSpacing) / widget.items.length;

          return Stack(
            children: [
              Obx(() {
                final position = selected * tabWidth + (selected * 12);
                return AnimatedPositionedDirectional(
                  duration: (100 * widget.items.length).milliseconds,
                  start: position,
                  top: 0,
                  bottom: 0,
                  child: Container(
                    width: tabWidth,
                    decoration: BoxDecoration(
                      color: context.appColorScheme.secondaryColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                );
              }),
              Row(
                spacing: 12,
                children: List.generate(widget.items.length, (index) {
                  return Expanded(
                    child: InkWell(
                      onTap: () => selected = index,
                      borderRadius: BorderRadius.circular(8),
                      child: Obx(
                        () => Container(
                          decoration: BoxDecoration(
                            color:
                                selected == index
                                    ? null
                                    : context
                                        .appColorScheme
                                        .secondaryColor
                                        .shade100
                                        .withValues(alpha: .5),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: AnimatedDefaultTextStyle(
                            duration: 300.milliseconds,
                            style: context.textTheme.titleMedium!.copyWith(
                              color:
                                  selected == index
                                      ? context.appColorScheme.onPrimaryColor
                                      : context.appColorScheme.secondaryColor,
                            ),
                            child: Center(child: widget.items[index]),
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ],
          );
        },
      ),
    );
  }
}
