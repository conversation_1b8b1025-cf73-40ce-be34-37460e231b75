import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/features/categories/widgets/category_card/category_card.dart';
import 'package:tredo/features/providers/widgets/provider_card/provider_card.dart';

class ProvidersList extends StatelessWidget {
  const ProvidersList({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  tr(LocaleKeys.service_providers),
                  style: context.textTheme.titleLarge,
                ),
              ),
              TextButton(
                onPressed: () {},
                child: Text(
                  tr(LocaleKeys.show_all),
                  style: context.textTheme.bodyMedium!.copyWith(
                    color: context.appColorScheme.secondaryColor.shade700,
                  ),
                ),
              ),
            ],
          ),
        ),
        const Gap(16),
        Si<PERSON><PERSON><PERSON>(
          height: 270,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: 10,
            separatorBuilder: (_, __) => const Gap(12),
            itemBuilder: (context, index) => const ProviderCard(),
          ),
        ),
      ],
    );
  }
}
